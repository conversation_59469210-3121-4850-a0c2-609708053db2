const Meal = require('../models/Meal');

// Get all meals (admin and sub-admin access)
exports.getAllMeals = async (req, res) => {
  try {
    const { page = 1, limit = 20, sortBy = 'name', sortOrder = 'asc', search = '' } = req.query;
    
    // Build search query
    const searchQuery = search ? {
      $or: [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } }
      ]
    } : {};

    // Build sort object
    const sortOptions = {};
    const validSortFields = ['name', 'category', 'calories', 'price', 'prepTime', 'createdAt'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'name';
    const order = sortOrder === 'desc' ? -1 : 1;
    sortOptions[sortField] = order;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get meals with pagination
    const meals = await Meal.find(searchQuery)
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalMeals = await Meal.countDocuments(searchQuery);
    const totalPages = Math.ceil(totalMeals / parseInt(limit));

    res.json({
      meals,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalMeals,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Get all meals error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get meal by ID (admin and sub-admin access)
exports.getMealById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const meal = await Meal.findById(id);
    if (!meal) {
      return res.status(404).json({ message: 'Meal not found' });
    }

    res.json(meal);
  } catch (error) {
    console.error('Get meal by ID error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Create new meal (admin and sub-admin access)
exports.createMeal = async (req, res) => {
  try {
    const mealData = req.body;
    
    // Validate required fields
    const requiredFields = ['name', 'category'];
    for (const field of requiredFields) {
      if (!mealData[field]) {
        return res.status(400).json({ message: `${field} is required` });
      }
    }

    // Check if meal with same name already exists
    const existingMeal = await Meal.findOne({ name: mealData.name });
    if (existingMeal) {
      return res.status(400).json({ message: 'Meal with this name already exists' });
    }

    // Add creator information
    mealData.createdBy = req.user.id;
    mealData.createdAt = new Date();

    const newMeal = new Meal(mealData);
    const savedMeal = await newMeal.save();

    res.status(201).json({
      message: 'Meal created successfully',
      meal: savedMeal
    });
  } catch (error) {
    console.error('Create meal error:', error);
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ message: 'Validation error', errors: validationErrors });
    }
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Update meal (admin and sub-admin access)
exports.updateMeal = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Check if meal exists
    const existingMeal = await Meal.findById(id);
    if (!existingMeal) {
      return res.status(404).json({ message: 'Meal not found' });
    }

    // If updating name, check for duplicates
    if (updateData.name && updateData.name !== existingMeal.name) {
      const duplicateMeal = await Meal.findOne({ name: updateData.name, _id: { $ne: id } });
      if (duplicateMeal) {
        return res.status(400).json({ message: 'Meal with this name already exists' });
      }
    }

    // Add update information
    updateData.updatedBy = req.user.id;
    updateData.updatedAt = new Date();

    const updatedMeal = await Meal.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    res.json({
      message: 'Meal updated successfully',
      meal: updatedMeal
    });
  } catch (error) {
    console.error('Update meal error:', error);
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ message: 'Validation error', errors: validationErrors });
    }
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Delete meal (admin and sub-admin access)
exports.deleteMeal = async (req, res) => {
  try {
    const { id } = req.params;

    const meal = await Meal.findById(id);
    if (!meal) {
      return res.status(404).json({ message: 'Meal not found' });
    }

    await Meal.findByIdAndDelete(id);

    res.json({
      message: 'Meal deleted successfully',
      deletedMeal: {
        id: meal._id,
        name: meal.name
      }
    });
  } catch (error) {
    console.error('Delete meal error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get meal statistics (admin and sub-admin access)
exports.getMealStatistics = async (req, res) => {
  try {
    const totalMeals = await Meal.countDocuments();
    
    const mealsByCategory = await Meal.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    const averageCalories = await Meal.aggregate([
      {
        $group: {
          _id: null,
          avgCalories: { $avg: '$calories' }
        }
      }
    ]);

    const recentMeals = await Meal.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name category calories createdAt');

    res.json({
      totalMeals,
      mealsByCategory,
      averageCalories: averageCalories[0]?.avgCalories || 0,
      recentMeals
    });
  } catch (error) {
    console.error('Get meal statistics error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
