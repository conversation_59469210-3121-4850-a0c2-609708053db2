const User = require('../models/User');
const Admin = require('../models/Admin');

// Middleware to check if user is admin
const adminAuth = async (req, res, next) => {
  try {
    // First check if user is authenticated (should be done by auth middleware first)
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Get user from database to check admin status
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if user has admin flag
    if (!user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
    }

    // Optionally check Admin collection for additional permissions
    const adminRecord = await Admin.findOne({ user: user._id, isActive: true });
    if (adminRecord) {
      req.adminRole = adminRecord.role;
      req.adminPermissions = adminRecord.permissions;
      
      // Update last activity
      adminRecord.lastActivity = new Date();
      await adminRecord.save();
    }

    // Add user info to request
    req.adminUser = user;
    next();
  } catch (error) {
    console.error('Admin auth error:', error);
    res.status(500).json({ message: 'Server error during admin authentication' });
  }
};

// Middleware to check specific admin permissions
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.adminPermissions || !req.adminPermissions.includes(permission)) {
      return res.status(403).json({ 
        message: `Access denied. Required permission: ${permission}` 
      });
    }
    next();
  };
};

// Middleware to check admin role level
const requireRole = (minRole) => {
  const roleHierarchy = {
    'moderator': 1,
    'sub_admin': 2,
    'admin': 3,
    'super_admin': 4
  };

  return (req, res, next) => {
    const userRoleLevel = roleHierarchy[req.adminRole] || 0;
    const requiredRoleLevel = roleHierarchy[minRole] || 0;

    if (userRoleLevel < requiredRoleLevel) {
      return res.status(403).json({
        message: `Access denied. Required role: ${minRole}`
      });
    }
    next();
  };
};

// Middleware to check if user is admin or sub-admin
const adminOrSubAdminAuth = async (req, res, next) => {
  try {
    // First check if user is authenticated (should be done by auth middleware first)
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Get user from database to check admin status
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if user has admin flag
    if (!user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
    }

    // Check Admin collection for role and permissions
    const adminRecord = await Admin.findOne({ user: user._id, isActive: true });
    if (adminRecord) {
      req.adminRole = adminRecord.role;
      req.adminPermissions = adminRecord.permissions;

      // Check if user is admin, sub_admin, or super_admin
      if (!['admin', 'sub_admin', 'super_admin'].includes(adminRecord.role)) {
        return res.status(403).json({ message: 'Access denied. Admin or Sub-Admin privileges required.' });
      }

      // Update last activity
      adminRecord.lastActivity = new Date();
      await adminRecord.save();
    } else {
      // If no admin record but user.isAdmin is true, treat as regular admin
      req.adminRole = 'admin';
      req.adminPermissions = ['user_management', 'analytics_view', 'system_health'];
    }

    // Add user info to request
    req.adminUser = user;
    next();
  } catch (error) {
    console.error('Admin auth error:', error);
    res.status(500).json({ message: 'Server error during admin authentication' });
  }
};

// Middleware to check if user has read-only access (sub-admin for most features)
const requireReadOnlyAccess = (req, res, next) => {
  if (!req.adminRole || !['admin', 'sub_admin', 'super_admin'].includes(req.adminRole)) {
    return res.status(403).json({
      message: 'Access denied. Admin or Sub-Admin privileges required.'
    });
  }
  next();
};

// Middleware to check if user has full admin access (not sub-admin for write operations)
const requireFullAdminAccess = (req, res, next) => {
  if (!req.adminRole || !['admin', 'super_admin'].includes(req.adminRole)) {
    return res.status(403).json({
      message: 'Access denied. Full admin privileges required.'
    });
  }
  next();
};

module.exports = {
  adminAuth,
  adminOrSubAdminAuth,
  requirePermission,
  requireRole,
  requireReadOnlyAccess,
  requireFullAdminAccess
};
