const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const adminMealController = require('../controllers/adminMealController');
const auth = require('../middleware/auth');
const { adminAuth, adminOrSubAdminAuth, requirePermission, requireFullAdminAccess, requireReadOnlyAccess } = require('../middleware/adminAuth');

console.log('=== ADMIN ROUTES FILE LOADED ===');

// Apply auth middleware to all admin routes
router.use(auth);

// Get all users (admin only)
router.get('/users', adminController.getAllUsers);

// User management routes
console.log('Setting up user management routes...');
router.post('/users', adminController.createUser);
router.put('/users/:userId', adminController.updateUser);
console.log('Setting up disable/enable routes...');
router.put('/users/:userId/disable', adminController.disableUser);
router.put('/users/:userId/enable', adminController.enableUser);
console.log('Disable/enable routes set up');
router.put('/users/:userId/make-admin', adminController.makeUserAdmin);
router.put('/users/:userId/make-sub-admin', adminController.makeUserSubAdmin);
router.put('/users/:userId/remove-admin', adminController.removeUserAdmin);

// Get user signup statistics (admin only)
router.get('/stats/signups', adminController.getUserSignupStats);

// Get system overview (admin only)
router.get('/overview', adminController.getSystemOverview);

// Generate user registration report
router.get('/reports/registrations', adminController.getUserRegistrationReport);

// Get system health information
router.get('/system/health', adminController.getSystemHealth);

// Geolocation analytics
router.get('/analytics/geolocation', adminController.getGeolocationAnalytics);

// Meal management routes (admin and sub-admin access)
console.log('Setting up meal management routes...');
router.use('/meals', adminOrSubAdminAuth); // Apply admin/sub-admin auth to all meal routes

// Get all meals (read access for admin and sub-admin)
router.get('/meals', requirePermission('meal_management'), adminMealController.getAllMeals);

// Create new meal (sub-admin and admin access)
router.post('/meals', requirePermission('meal_management'), adminMealController.createMeal);

// Update meal (sub-admin and admin access)
router.put('/meals/:id', requirePermission('meal_management'), adminMealController.updateMeal);

// Delete meal (sub-admin and admin access)
router.delete('/meals/:id', requirePermission('meal_management'), adminMealController.deleteMeal);

// Get meal by ID (read access for admin and sub-admin)
router.get('/meals/:id', requirePermission('meal_management'), adminMealController.getMealById);

// Get meal statistics (read access for admin and sub-admin)
router.get('/meals-stats', requirePermission('meal_management'), adminMealController.getMealStatistics);

console.log('=== ADMIN ROUTES SETUP COMPLETE ===');
module.exports = router;
