.meal-management {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.meal-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.meal-management-header h2 {
  color: #333;
  margin: 0;
}

.btn-add-meal {
  background: #20C5AF;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.btn-add-meal:hover {
  background: #1ba896;
}

.meal-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card p {
  margin: 0;
  font-size: 32px;
  font-weight: bold;
  color: #20C5AF;
}

.meal-controls {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-input,
.filter-select,
.sort-select,
.order-select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-input:focus,
.filter-select:focus,
.sort-select:focus,
.order-select:focus {
  outline: none;
  border-color: #20C5AF;
  box-shadow: 0 0 0 2px rgba(32, 197, 175, 0.2);
}

.meals-table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.meals-table {
  width: 100%;
  border-collapse: collapse;
}

.meals-table th {
  background: #f8f9fa;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #dee2e6;
}

.meals-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.meals-table tr:hover {
  background: #f8f9fa;
}

.difficulty-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.difficulty-badge.easy {
  background: #d4edda;
  color: #155724;
}

.difficulty-badge.medium {
  background: #fff3cd;
  color: #856404;
}

.difficulty-badge.hard {
  background: #f8d7da;
  color: #721c24;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-edit,
.btn-delete {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-edit {
  background: #007bff;
  color: white;
}

.btn-edit:hover {
  background: #0056b3;
}

.btn-delete {
  background: #dc3545;
  color: white;
}

.btn-delete:hover {
  background: #c82333;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.pagination button:hover:not(:disabled) {
  background: #20C5AF;
  color: white;
  border-color: #20C5AF;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination span {
  font-weight: 500;
  color: #666;
}

.loading,
.error {
  text-align: center;
  padding: 40px;
  font-size: 16px;
}

.loading {
  color: #666;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

/* Role badges for admin dashboard */
.role-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.role-badge.admin {
  background: #d4edda;
  color: #155724;
}

.role-badge.sub_admin {
  background: #cce5ff;
  color: #004085;
}

.role-badge.user {
  background: #e2e3e5;
  color: #383d41;
}

/* Button styles for sub-admin actions */
.btn-make-sub-admin {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  margin-left: 4px;
  transition: background-color 0.3s;
}

.btn-make-sub-admin:hover:not(:disabled) {
  background: #138496;
}

.btn-make-sub-admin:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .meal-controls {
    grid-template-columns: 1fr;
  }
  
  .meal-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .meals-table-container {
    overflow-x: auto;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
