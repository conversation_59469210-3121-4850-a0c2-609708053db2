import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './MealManagement.css';

function MealManagement() {
  const [meals, setMeals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [mealStats, setMealStats] = useState(null);

  // Form state for add/edit meal
  const [mealForm, setMealForm] = useState({
    name: '',
    description: '',
    category: '',
    calories: '',
    protein: '',
    carbs: '',
    fat: '',
    fiber: '',
    price: '',
    prepTime: '',
    cookTime: '',
    servings: '',
    difficulty: 'easy',
    ingredients: [],
    instructions: [],
    tags: [],
    imageUrl: '',
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isDairyFree: false
  });

  const categories = [
    'Breakfast', 'Lunch', 'Dinner', 'Snack', 'Dessert', 
    'Appetizer', 'Soup', 'Salad', 'Main Course', 'Side Dish'
  ];

  const difficulties = ['easy', 'medium', 'hard'];

  useEffect(() => {
    fetchMeals();
    fetchMealStats();
  }, [currentPage, searchTerm, categoryFilter, sortBy, sortOrder]);

  const fetchMeals = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const config = {
        headers: { 'x-auth-token': token },
        params: {
          page: currentPage,
          limit: 20,
          search: searchTerm,
          category: categoryFilter !== 'all' ? categoryFilter : '',
          sortBy,
          sortOrder
        }
      };

      const response = await axios.get('http://localhost:5000/api/admin/meals', config);
      setMeals(response.data.meals);
      setTotalPages(response.data.pagination.totalPages);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch meals');
      setLoading(false);
      console.error('Fetch meals error:', err);
    }
  };

  const fetchMealStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const config = { headers: { 'x-auth-token': token } };
      const response = await axios.get('http://localhost:5000/api/admin/meals-stats', config);
      setMealStats(response.data);
    } catch (err) {
      console.error('Fetch meal stats error:', err);
    }
  };

  const handleAddMeal = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('token');
      const config = { headers: { 'x-auth-token': token } };
      
      await axios.post('http://localhost:5000/api/admin/meals', mealForm, config);
      
      setShowAddModal(false);
      resetForm();
      fetchMeals();
      fetchMealStats();
      alert('Meal added successfully!');
    } catch (err) {
      alert('Error adding meal: ' + (err.response?.data?.message || err.message));
    }
  };

  const handleEditMeal = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('token');
      const config = { headers: { 'x-auth-token': token } };
      
      await axios.put(`http://localhost:5000/api/admin/meals/${selectedMeal._id}`, mealForm, config);
      
      setShowEditModal(false);
      setSelectedMeal(null);
      resetForm();
      fetchMeals();
      alert('Meal updated successfully!');
    } catch (err) {
      alert('Error updating meal: ' + (err.response?.data?.message || err.message));
    }
  };

  const handleDeleteMeal = async (mealId, mealName) => {
    if (window.confirm(`Are you sure you want to delete "${mealName}"?`)) {
      try {
        const token = localStorage.getItem('token');
        const config = { headers: { 'x-auth-token': token } };
        
        await axios.delete(`http://localhost:5000/api/admin/meals/${mealId}`, config);
        
        fetchMeals();
        fetchMealStats();
        alert('Meal deleted successfully!');
      } catch (err) {
        alert('Error deleting meal: ' + (err.response?.data?.message || err.message));
      }
    }
  };

  const openEditModal = (meal) => {
    setSelectedMeal(meal);
    setMealForm({
      name: meal.name || '',
      description: meal.description || '',
      category: meal.category || '',
      calories: meal.calories || '',
      protein: meal.protein || '',
      carbs: meal.carbs || '',
      fat: meal.fat || '',
      fiber: meal.fiber || '',
      price: meal.price || '',
      prepTime: meal.prepTime || '',
      cookTime: meal.cookTime || '',
      servings: meal.servings || '',
      difficulty: meal.difficulty || 'easy',
      ingredients: meal.ingredients || [],
      instructions: meal.instructions || [],
      tags: meal.tags || [],
      imageUrl: meal.imageUrl || '',
      isVegetarian: meal.isVegetarian || false,
      isVegan: meal.isVegan || false,
      isGlutenFree: meal.isGlutenFree || false,
      isDairyFree: meal.isDairyFree || false
    });
    setShowEditModal(true);
  };

  const resetForm = () => {
    setMealForm({
      name: '',
      description: '',
      category: '',
      calories: '',
      protein: '',
      carbs: '',
      fat: '',
      fiber: '',
      price: '',
      prepTime: '',
      cookTime: '',
      servings: '',
      difficulty: 'easy',
      ingredients: [],
      instructions: [],
      tags: [],
      imageUrl: '',
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: false,
      isDairyFree: false
    });
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setMealForm(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const addIngredient = () => {
    setMealForm(prev => ({
      ...prev,
      ingredients: [...prev.ingredients, '']
    }));
  };

  const updateIngredient = (index, value) => {
    setMealForm(prev => ({
      ...prev,
      ingredients: prev.ingredients.map((ing, i) => i === index ? value : ing)
    }));
  };

  const removeIngredient = (index) => {
    setMealForm(prev => ({
      ...prev,
      ingredients: prev.ingredients.filter((_, i) => i !== index)
    }));
  };

  const addInstruction = () => {
    setMealForm(prev => ({
      ...prev,
      instructions: [...prev.instructions, '']
    }));
  };

  const updateInstruction = (index, value) => {
    setMealForm(prev => ({
      ...prev,
      instructions: prev.instructions.map((inst, i) => i === index ? value : inst)
    }));
  };

  const removeInstruction = (index) => {
    setMealForm(prev => ({
      ...prev,
      instructions: prev.instructions.filter((_, i) => i !== index)
    }));
  };

  if (loading) return <div className="loading">Loading meals...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="meal-management">
      <div className="meal-management-header">
        <h2>Meal Management</h2>
        <button 
          className="btn-add-meal"
          onClick={() => setShowAddModal(true)}
        >
          Add New Meal
        </button>
      </div>

      {/* Meal Statistics */}
      {mealStats && (
        <div className="meal-stats">
          <div className="stat-card">
            <h3>Total Meals</h3>
            <p>{mealStats.totalMeals}</p>
          </div>
          <div className="stat-card">
            <h3>Average Calories</h3>
            <p>{Math.round(mealStats.averageCalories)}</p>
          </div>
          <div className="stat-card">
            <h3>Categories</h3>
            <p>{mealStats.mealsByCategory.length}</p>
          </div>
        </div>
      )}

      {/* Search and Filter Controls */}
      <div className="meal-controls">
        <input
          type="text"
          placeholder="Search meals..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="search-input"
        />
        
        <select
          value={categoryFilter}
          onChange={(e) => setCategoryFilter(e.target.value)}
          className="filter-select"
        >
          <option value="all">All Categories</option>
          {categories.map(cat => (
            <option key={cat} value={cat}>{cat}</option>
          ))}
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="sort-select"
        >
          <option value="name">Sort by Name</option>
          <option value="category">Sort by Category</option>
          <option value="calories">Sort by Calories</option>
          <option value="createdAt">Sort by Date</option>
        </select>

        <select
          value={sortOrder}
          onChange={(e) => setSortOrder(e.target.value)}
          className="order-select"
        >
          <option value="asc">Ascending</option>
          <option value="desc">Descending</option>
        </select>
      </div>

      {/* Meals Table */}
      <div className="meals-table-container">
        <table className="meals-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Category</th>
              <th>Calories</th>
              <th>Prep Time</th>
              <th>Difficulty</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {meals.map(meal => (
              <tr key={meal._id}>
                <td>{meal.name}</td>
                <td>{meal.category}</td>
                <td>{meal.calories || 'N/A'}</td>
                <td>{meal.prepTime || 'N/A'} min</td>
                <td>
                  <span className={`difficulty-badge ${meal.difficulty}`}>
                    {meal.difficulty}
                  </span>
                </td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn-edit"
                      onClick={() => openEditModal(meal)}
                    >
                      Edit
                    </button>
                    <button
                      className="btn-delete"
                      onClick={() => handleDeleteMeal(meal._id, meal.name)}
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button
            disabled={currentPage === 1}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            Previous
          </button>
          <span>Page {currentPage} of {totalPages}</span>
          <button
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Next
          </button>
        </div>
      )}

      {/* Add/Edit Modal will be implemented in the next part */}
    </div>
  );
}

export default MealManagement;
